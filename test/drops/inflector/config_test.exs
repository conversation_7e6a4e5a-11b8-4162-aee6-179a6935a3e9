defmodule Drops.InflectorTest do
  use ExUnit.Case
  doctest Drops.Inflector

  alias Drops.Inflector

  describe "custom inflector" do
    defmodule Test.Inflector do
      use Drops.Inflector,
        plural: [
          {"virus", "viruses"}
        ],
        singular: [
          {"thieves", "thief"}
        ],
        uncountable: [
          "dry-inflector"
        ]
    end

    test "respects customized configuration" do
      assert Test.Inflector.pluralize("virus") == "viruses"
    end
  end
end
